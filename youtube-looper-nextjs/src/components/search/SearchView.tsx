'use client'

import { useState } from 'react'
import { SearchInput } from './SearchInput'
import { SearchResults } from './SearchResults'
import { youtubeService } from '@/lib/services/youtube'
import { VideoSearchResult } from '@/lib/types/video'
import { useAuth } from '@/hooks/useAuth'
import { useQueue } from '@/hooks/useQueue'

export function SearchView() {
  const { user, isAuthenticated } = useAuth()
  const { saveQueue, items } = useQueue()

  // Search state
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<VideoSearchResult[]>([])
  const [error, setError] = useState<string | null>(null)

  // Queue creation state
  const [isCreationMode, setIsCreationMode] = useState(false)
  const [queueTitle, setQueueTitle] = useState('')
  const [queueDescription, setQueueDescription] = useState('')
  const [isPublic, setIsPublic] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([])
      setError(null)
      return
    }

    setIsSearching(true)
    setSearchQuery(query)
    setError(null)

    try {
      console.log('🔍 Searching YouTube for:', query)
      const results = await youtubeService.searchVideos(query, 20)
      setSearchResults(results)
      console.log('✅ Found', results.length, 'videos')
    } catch (error: any) {
      console.error('❌ Search error:', error)
      setError(error.message || 'Failed to search videos')
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }

  const enterCreationMode = () => {
    setIsCreationMode(true)
    console.log('📝 Entered queue creation mode')
  }

  const exitCreationMode = () => {
    setIsCreationMode(false)
    setQueueTitle('')
    setQueueDescription('')
    setIsPublic(false)
    console.log('❌ Exited queue creation mode')
  }

  const handleCreateQueue = async () => {
    if (!queueTitle.trim() || items.length === 0) return

    try {
      setIsSaving(true)
      const queueId = await saveQueue(queueTitle.trim(), isPublic)

      if (queueId) {
        console.log('✅ Queue created successfully')
        exitCreationMode()
      }
    } catch (error) {
      console.error('Failed to create queue:', error)
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Queue Creation Mode Header */}
      {isCreationMode && (
        <div className="glassmorphism rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-primary-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                +
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Creating New Queue</h3>
                <p className="text-dark-300">Add videos to create your new queue</p>
              </div>
            </div>
            <button
              onClick={exitCreationMode}
              className="p-2 text-dark-300 hover:text-white transition-colors"
              title="Cancel"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Queue Creation Form */}
      {isCreationMode && (
        <div className="glassmorphism rounded-2xl p-6">
          <div className="space-y-4">
            <div>
              <input
                type="text"
                value={queueTitle}
                onChange={(e) => setQueueTitle(e.target.value)}
                placeholder="Enter queue title..."
                className="input-field"
              />
              <div className="flex items-center space-x-2 mt-2 text-sm text-dark-300">
                <span className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center text-white text-xs font-bold">1</span>
                <span>Enter a title, then search for videos below</span>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="isPublic"
                checked={isPublic}
                onChange={(e) => setIsPublic(e.target.checked)}
                className="w-4 h-4 text-primary-600 bg-dark-700 border-dark-600 rounded focus:ring-primary-500"
              />
              <label htmlFor="isPublic" className="text-white">
                Make this queue public
              </label>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleCreateQueue}
                disabled={!queueTitle.trim() || items.length === 0 || isSaving}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSaving ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating...
                  </>
                ) : (
                  <>
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor" className="mr-2">
                      <path d="M17 3H5C3.89 3 3 3.9 3 5V19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V7L17 3M19 19H5V5H16.17L19 7.83V19M12 12C10.34 12 9 13.34 9 15S10.34 18 12 18 15 16.66 15 15 13.66 12 12 12M6 6H15V10H6V6Z"/>
                    </svg>
                    Save Queue
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* New Queue Button (when not in creation mode) */}
      {!isCreationMode && (
        <div className="glassmorphism rounded-2xl p-8 text-center">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-white mb-2">Create Your Music Queue</h2>
            <p className="text-dark-300">Build custom playlists with your favorite YouTube videos</p>
          </div>
          <button
            onClick={enterCreationMode}
            className="btn-primary text-lg px-8 py-4"
            title="Create New Queue"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" className="mr-3">
              <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </svg>
            Create New Queue
          </button>
        </div>
      )}

      {/* Search Section (only show when in creation mode) */}
      {isCreationMode && (
        <>
          {/* Search Header */}
          <div className="glassmorphism rounded-2xl p-6">
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-white mb-2">
                Search YouTube Videos
              </h1>
              <p className="text-dark-300">
                Find and add videos to your queue
              </p>
            </div>

            <SearchInput
              onSearch={handleSearch}
              isLoading={isSearching}
              placeholder="Search for videos, artists, or songs..."
            />
          </div>

          {/* Error Message */}
          {error && (
            <div className="glassmorphism rounded-2xl p-6">
              <div className="flex items-center space-x-3 text-red-400">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                </svg>
                <span>{error}</span>
              </div>
            </div>
          )}

          {/* Search Results */}
          <SearchResults
            results={searchResults}
            isLoading={isSearching}
            query={searchQuery}
            error={error}
          />
        </>
      )}
    </div>
  )
}
