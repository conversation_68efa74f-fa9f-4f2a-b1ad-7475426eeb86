'use client'

import { useState } from 'react'
import { SearchInput } from './SearchInput'
import { SearchResults } from './SearchResults'
import { youtubeService } from '@/lib/services/youtube'
import { VideoSearchResult } from '@/lib/types/video'

export function SearchView() {
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<VideoSearchResult[]>([])
  const [error, setError] = useState<string | null>(null)

  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([])
      setError(null)
      return
    }

    setIsSearching(true)
    setSearchQuery(query)
    setError(null)

    try {
      console.log('🔍 Searching YouTube for:', query)
      const results = await youtubeService.searchVideos(query, 20)
      setSearchResults(results)
      console.log('✅ Found', results.length, 'videos')
    } catch (error: any) {
      console.error('❌ Search error:', error)
      setError(error.message || 'Failed to search videos')
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Search Header */}
      <div className="glassmorphism rounded-2xl p-6">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">
            Search YouTube Videos
          </h1>
          <p className="text-dark-300">
            Find and add videos to your queue
          </p>
        </div>
        
        <SearchInput
          onSearch={handleSearch}
          isLoading={isSearching}
          placeholder="Search for videos, artists, or songs..."
        />
      </div>

      {/* Error Message */}
      {error && (
        <div className="glassmorphism rounded-2xl p-6">
          <div className="flex items-center space-x-3 text-red-400">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
            </svg>
            <span>{error}</span>
          </div>
        </div>
      )}

      {/* Search Results */}
      <SearchResults
        results={searchResults}
        isLoading={isSearching}
        query={searchQuery}
        error={error}
      />
    </div>
  )
}
